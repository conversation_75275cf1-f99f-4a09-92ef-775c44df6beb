install:
	@echo "📦 Installing Python dependencies..."
	python3 -m pip install -r requirements.txt
	@echo "✅ Dependencies installed successfully!"
	@echo ""
	@echo "🔍 Testing installation..."
	@python3 -c "from obsidian_sync import ObsidianToGoogleSync; print('✅ Modular architecture ready!')" || echo "⚠️  Modular architecture not available, using legacy only"

create-onenote-config:
	@echo "⚙️  Creating OneNote sync configuration..."
	python3 obsidian_to_onenote_sync.py --create-config
	@echo "✅ Config created! Edit sync_config.json with your Microsoft App credentials."

create-google-config:
	@echo "⚙️  Creating Google sync configuration (new method)..."
	python3 main.py --create-config
	@echo "✅ Config created! Edit google_sync_config.json with your settings."

sync-google:
	@echo "🔄 Starting Obsidian to Google Docs/Drive sync (new modular architecture)..."
	@echo "📁 Using main.py entry point"
	python3 main.py --obsidian-path ./obsidian --credentials credentials.json
	@echo "✅ Sync completed using new architecture!"

test:
	@echo "🧪 Running comprehensive tests..."
	python3 test_all_comprehensive.py
	@echo "✅ All tests completed!"
