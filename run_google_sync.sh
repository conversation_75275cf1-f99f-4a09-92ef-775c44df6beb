#!/bin/bash

# Obsidian to Google Docs/Drive Sync Runner Script
# Updated to support both modular architecture and legacy methods

echo "🔄 Obsidian to Google Docs/Drive Sync Tool"
echo "=========================================="
echo "🎉 Now supports both new modular architecture and legacy methods!"
echo ""

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.7+ first."
    exit 1
fi

# Check if pip3 is installed
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is not installed. Please install pip3 first."
    exit 1
fi

# Function to check if modular architecture is available
check_modular_architecture() {
    python3 -c "from obsidian_sync import ObsidianToGoogleSync" 2>/dev/null
    return $?
}

# Determine which method to use
if check_modular_architecture; then
    echo "✅ Modular architecture detected - using new main.py"
    SYNC_METHOD="new"
    SYNC_COMMAND="python3 main.py"
else
    echo "⚠️  Modular architecture not available - using legacy method"
    SYNC_METHOD="legacy"
    SYNC_COMMAND="python3 obsidian_to_google_sync.py"
fi

echo ""

# Install dependencies if requirements.txt exists
if [ -f "requirements.txt" ]; then
    echo "📦 Installing Python dependencies..."
    pip3 install -r requirements.txt
    
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies. Please check your Python environment."
        exit 1
    fi
    echo "✅ Dependencies installed successfully!"
else
    echo "⚠️  requirements.txt not found. Make sure all dependencies are installed."
fi

# Check if config file exists
if [ ! -f "google_sync_config.json" ]; then
    echo "📝 Creating configuration file..."
    
    if [ "$SYNC_METHOD" = "new" ]; then
        python3 main.py --create-config
    else
        python3 obsidian_to_google_sync.py --create-config
    fi
    
    echo ""
    echo "⚠️  Configuration file created: google_sync_config.json"
    echo "Please edit this file with your settings before running sync."
    echo ""
    echo "Next steps:"
    echo "1. Download credentials.json from Google Cloud Console"
    echo "2. Edit google_sync_config.json with your Obsidian vault path"
    echo "3. Run this script again to start sync"
    echo ""
    echo "📚 For more help:"
    echo "   - Read docs/DOCUMENTATION.md"
    echo "   - Check MIGRATION.md for new features"
    echo ""
    exit 0
fi

# Check if credentials file exists
CREDENTIALS_FILE=$(python3 -c "
import json
try:
    with open('google_sync_config.json', 'r') as f:
        config = json.load(f)
    print(config.get('credentials_path', 'credentials.json'))
except:
    print('credentials.json')
")

if [ ! -f "$CREDENTIALS_FILE" ]; then
    echo "❌ Google credentials file not found: $CREDENTIALS_FILE"
    echo ""
    echo "Please follow these steps:"
    echo "1. Go to https://console.cloud.google.com/"
    echo "2. Create a new project or select existing one"
    echo "3. Enable Google Drive API and Google Docs API"
    echo "4. Create OAuth 2.0 Client ID credentials"
    echo "5. Download the JSON file and save as $CREDENTIALS_FILE"
    echo ""
    exit 1
fi

# Check if Obsidian vault exists
OBSIDIAN_PATH=$(python3 -c "
import json
try:
    with open('google_sync_config.json', 'r') as f:
        config = json.load(f)
    print(config.get('obsidian_path', './obsidian'))
except:
    print('./obsidian')
")

if [ ! -d "$OBSIDIAN_PATH" ]; then
    echo "❌ Obsidian vault not found: $OBSIDIAN_PATH"
    echo "Please update the obsidian_path in google_sync_config.json"
    exit 1
fi

# Count markdown files
MD_COUNT=$(find "$OBSIDIAN_PATH" -name "*.md" -not -path "*/.*" | wc -l)
echo "📚 Found $MD_COUNT markdown files in Obsidian vault"

if [ $MD_COUNT -eq 0 ]; then
    echo "⚠️  No markdown files found. Please check your Obsidian vault path."
    exit 1
fi

echo ""
echo "🚀 Starting sync process using $SYNC_METHOD method..."
echo "This may take a while depending on the number of files..."
echo ""

# Run the sync based on available method
if [ "$SYNC_METHOD" = "new" ]; then
    echo "📦 Using modular architecture (main.py)"
    python3 main.py --config google_sync_config.json "$@"
else
    echo "📦 Using legacy method (obsidian_to_google_sync.py)"
    python3 obsidian_to_google_sync.py "$@"
fi

SYNC_RESULT=$?

echo ""
if [ $SYNC_RESULT -eq 0 ]; then
    echo "✅ Sync completed successfully!"
    echo "📁 Check your Google Drive for the synced files"
    echo "📋 Log file: google_sync.log"
    
    if [ "$SYNC_METHOD" = "new" ]; then
        echo ""
        echo "🎉 You used the new modular architecture!"
        echo "   Benefits: Better error handling, easier debugging, modular design"
        echo "   Learn more: Read MIGRATION.md"
    fi
else
    echo "❌ Sync failed!"
    echo "📋 Check google_sync.log for error details"
    
    if [ "$SYNC_METHOD" = "new" ]; then
        echo ""
        echo "🔄 Fallback option available:"
        echo "   Try legacy method: python3 obsidian_to_google_sync.py"
    fi
fi

echo ""
echo "🔗 Useful links:"
echo "   Google Drive: https://drive.google.com/"
echo "   Google Docs: https://docs.google.com/"
echo "   Documentation: docs/DOCUMENTATION.md"
echo "   Migration Guide: MIGRATION.md"
echo ""

# Show retry option if there were failures
if [ -f "failed_uploads.json" ]; then
    echo "⚠️  Some files failed to upload."
    echo "🔄 To retry failed uploads:"
    if [ "$SYNC_METHOD" = "new" ]; then
        echo "   python3 main.py --retry-failed"
    else
        echo "   python3 obsidian_to_google_sync.py --retry-failed"
    fi
    echo ""
fi
