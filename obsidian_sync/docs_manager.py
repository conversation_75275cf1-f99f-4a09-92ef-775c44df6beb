"""
Google Docs Manager
Handles document operations in Google Docs
"""

from typing import List, Dict, Optional, Tuple
from googleapiclient.errors import HttpError
from .utils import logger, grapheme_len


class GoogleDocsManager:
    """Manages Google Docs operations"""
    
    def __init__(self, docs_service, drive_service):
        """
        Initialize the docs manager
        
        Args:
            docs_service: Authenticated Google Docs service object
            drive_service: Authenticated Google Drive service object
        """
        self.docs_service = docs_service
        self.drive_service = drive_service
        self.failed_uploads = []
    
    def find_existing_doc(self, title: str, folder_id: str) -> Optional[str]:
        """Find existing Google Doc in folder"""
        try:
            query = f"name='{title}' and '{folder_id}' in parents and mimeType='application/vnd.google-apps.document' and trashed=false"

            results = self.drive_service.files().list(
                q=query,
                fields="files(id, name, modifiedTime)"
            ).execute()

            items = results.get('files', [])

            if items:
                doc_info = items[0]  # Get first match
                doc_id = doc_info['id']
                logger.info(f"Found existing document: {title} (ID: {doc_id})")
                return doc_id

            return None

        except HttpError as e:
            logger.error(f"Error searching for document {title}: {e}")
            return None

    def clear_document_content(self, doc_id: str) -> bool:
        """Clear all content from existing document"""
        try:
            # Get document to find content length
            document = self.docs_service.documents().get(documentId=doc_id).execute()
            content = document.get('body', {}).get('content', [])

            # Find the end index of content
            end_index = 1
            for element in content:
                if 'endIndex' in element:
                    end_index = max(end_index, element['endIndex'])

            if end_index > 1:
                # Delete all content except the first character (required by API)
                requests = [{
                    'deleteContentRange': {
                        'range': {
                            'startIndex': 1,
                            'endIndex': end_index - 1
                        }
                    }
                }]

                self.docs_service.documents().batchUpdate(
                    documentId=doc_id,
                    body={'requests': requests}
                ).execute()

                logger.info(f"Cleared content from document: {doc_id}")

            return True

        except HttpError as e:
            logger.error(f"Error clearing document content {doc_id}: {e}")
            return False

    def insert_image_into_doc(self, doc_id: str, image_url: str, image_name: str, insert_index: int = 1) -> bool:
        """Insert an image into Google Doc at specified position"""
        try:
            # Insert image request
            requests = [{
                'insertInlineImage': {
                    'location': {
                        'index': insert_index
                    },
                    'uri': image_url,
                    'objectSize': {
                        'height': {
                            'magnitude': 300,
                            'unit': 'PT'
                        },
                        'width': {
                            'magnitude': 400,
                            'unit': 'PT'
                        }
                    }
                }
            }]

            self.docs_service.documents().batchUpdate(
                documentId=doc_id,
                body={'requests': requests}
            ).execute()

            logger.info(f"Inserted image {image_name} into document")
            return True

        except HttpError as e:
            logger.error(f"Error inserting image {image_name}: {e}")
            return False

    def replace_placeholder_with_image(self, doc_id: str, placeholder_text: str, image_url: str, image_name: str) -> bool:
        """Replace a text placeholder with an image in the Google Doc"""
        try:
            # First, get the document content to find the placeholder
            document = self.docs_service.documents().get(documentId=doc_id).execute()
            content = document.get('body', {}).get('content', [])

            # Find the placeholder text in the document
            placeholder_start = None
            placeholder_end = None

            for element in content:
                if 'paragraph' in element:
                    paragraph = element['paragraph']
                    if 'elements' in paragraph:
                        for para_element in paragraph['elements']:
                            if 'textRun' in para_element:
                                text_run = para_element['textRun']
                                text_content = text_run.get('content', '')
                                if placeholder_text in text_content:
                                    # Found the placeholder
                                    start_index = para_element.get('startIndex', 0)
                                    text_before_placeholder = text_content[:text_content.index(placeholder_text)]
                                    placeholder_start = start_index + len(text_before_placeholder)
                                    placeholder_end = placeholder_start + len(placeholder_text)
                                    break
                    if placeholder_start is not None:
                        break

            if placeholder_start is None:
                logger.warning(f"Placeholder '{placeholder_text}' not found in document")
                return False

            # Create requests to replace placeholder with image
            requests = [
                # Insert image at placeholder position
                {
                    'insertInlineImage': {
                        'location': {
                            'index': placeholder_start
                        },
                        'uri': image_url,
                        'objectSize': {
                            'height': {
                                'magnitude': 300,
                                'unit': 'PT'
                            },
                            'width': {
                                'magnitude': 400,
                                'unit': 'PT'
                            }
                        }
                    }
                },
                # Delete the placeholder text (note: indices shift after image insertion)
                {
                    'deleteContentRange': {
                        'range': {
                            'startIndex': placeholder_start + 1,  # +1 because image was inserted
                            'endIndex': placeholder_end + 1
                        }
                    }
                }
            ]

            self.docs_service.documents().batchUpdate(
                documentId=doc_id,
                body={'requests': requests}
            ).execute()

            logger.info(f"Replaced placeholder '{placeholder_text}' with image {image_name}")
            return True

        except HttpError as e:
            logger.error(f"Error replacing placeholder with image {image_name}: {e}")
            return False

    def create_google_doc(self, title: str, content_requests: List[Dict], folder_id: str) -> Optional[str]:
        """Create or update a Google Doc with formatted content"""
        try:
            # Check if document already exists
            existing_doc_id = self.find_existing_doc(title, folder_id)

            if existing_doc_id:
                # Update existing document
                logger.info(f"Document {title} already exists, updating content...")

                # Clear existing content
                if self.clear_document_content(existing_doc_id):
                    doc_id = existing_doc_id
                    logger.info(f"Using existing Google Doc: {title} (ID: {doc_id})")
                else:
                    # If clearing fails, add to failed list and create new
                    self.failed_uploads.append({
                        'file_path': title,
                        'action': 'update_doc',
                        'error': 'Failed to clear existing document content'
                    })
                    logger.warning(f"Failed to update {title}, creating new document...")
                    existing_doc_id = None

            if not existing_doc_id:
                # Create new document
                doc = {
                    'title': title
                }

                document = self.docs_service.documents().create(body=doc).execute()
                doc_id = document.get('documentId')
                logger.info(f"Created new Google Doc: {title} (ID: {doc_id})")

                # Move document to the specified folder
                if folder_id:
                    self.drive_service.files().update(
                        fileId=doc_id,
                        addParents=folder_id,
                        removeParents='root'
                    ).execute()
                    logger.info(f"Moved document to folder: {folder_id}")

            # Apply content formatting if there are requests
            if content_requests:
                logger.info(f"Applying {len(content_requests)} formatting requests...")

                # Validate requests before sending
                validated_requests = self.validate_requests(content_requests)
                logger.info(f"Validated {len(validated_requests)}/{len(content_requests)} requests")

                if validated_requests:
                    try:
                        self.docs_service.documents().batchUpdate(
                            documentId=doc_id,
                            body={'requests': validated_requests}
                        ).execute()
                        logger.info(f"Applied {len(validated_requests)} requests successfully")
                    except HttpError as e:
                        logger.error(f"Error applying content requests: {e}")
                        # Try fallback method
                        if not self.apply_content_with_fallback(doc_id, validated_requests, title):
                            logger.error(f"Fallback method also failed for {title}")
                            self.failed_uploads.append({
                                'file_path': title,
                                'action': 'apply_content',
                                'error': str(e)
                            })
                else:
                    logger.warning(f"No valid requests to apply for {title}")

            logger.info(f"Successfully processed Google Doc: {title}")
            return doc_id

        except HttpError as e:
            logger.error(f"Error processing Google Doc {title}: {e}")
            # Add to failed list
            self.failed_uploads.append({
                'file_path': title,
                'action': 'create_doc',
                'error': str(e)
            })
            return None

    def validate_requests(self, requests: List[Dict]) -> List[Dict]:
        """Validate and clean requests before sending to Google Docs API"""
        valid_requests = []

        for i, request in enumerate(requests):
            try:
                # Check for common issues
                if 'updateTextStyle' in request:
                    text_style = request['updateTextStyle'].get('textStyle', {})

                    # Ensure fontFamily is in correct format
                    if 'fontFamily' in text_style:
                        font_family = text_style.pop('fontFamily')
                        text_style['weightedFontFamily'] = {'fontFamily': font_family}

                        # Update fields
                        fields = request['updateTextStyle'].get('fields', '')
                        fields = fields.replace('fontFamily', 'weightedFontFamily')
                        request['updateTextStyle']['fields'] = fields

                # Validate insertion indices for grapheme cluster issues
                if 'insertText' in request:
                    location = request['insertText'].get('location', {})
                    index = location.get('index', 0)
                    text = request['insertText'].get('text', '')

                    # Log details for debugging
                    logger.debug(f"Request {i}: insertText at index {index}, text length: {len(text)}, grapheme length: {grapheme_len(text)}")

                    # Ensure index is not negative and handle edge cases
                    if index < 0:
                        logger.warning(f"Request {i}: Negative index {index}, setting to 1")
                        location['index'] = 1
                    elif index == 0:
                        # Google Docs documents start at index 1, not 0
                        logger.debug(f"Request {i}: Index 0 changed to 1")
                        location['index'] = 1

                valid_requests.append(request)

            except Exception as e:
                logger.warning(f"Skipping invalid request {i}: {e}")
                logger.warning(f"Request content: {request}")
                continue

        logger.info(f"Validated {len(valid_requests)}/{len(requests)} requests")
        return valid_requests

    def apply_content_with_fallback(self, doc_id: str, requests: List[Dict], title: str) -> bool:
        """
        Fallback method to apply content when grapheme cluster errors occur.
        Tries to apply requests one by one and skips problematic ones.
        """
        try:
            logger.info(f"Applying {len(requests)} requests individually as fallback...")
            successful_requests = 0

            for i, request in enumerate(requests):
                try:
                    self.docs_service.documents().batchUpdate(
                        documentId=doc_id,
                        body={'requests': [request]}
                    ).execute()
                    successful_requests += 1
                    logger.debug(f"Applied request {i+1}/{len(requests)}")

                except HttpError as e:
                    logger.warning(f"Skipping request {i+1}: {e}")
                    # Try to adjust the request and retry once
                    adjusted_request = self.adjust_insert_request(request)
                    if adjusted_request:
                        try:
                            self.docs_service.documents().batchUpdate(
                                documentId=doc_id,
                                body={'requests': [adjusted_request]}
                            ).execute()
                            successful_requests += 1
                            logger.debug(f"Applied adjusted request {i+1}/{len(requests)}")
                        except HttpError as e2:
                            logger.warning(f"Adjusted request {i+1} also failed: {e2}")

            logger.info(f"Fallback completed: {successful_requests}/{len(requests)} requests applied")
            return successful_requests > 0

        except Exception as e:
            logger.error(f"Fallback method failed: {e}")
            return False

    def adjust_insert_request(self, request: Dict) -> Optional[Dict]:
        """
        Adjust an insertText request to avoid grapheme cluster issues.
        """
        if 'insertText' not in request:
            return None

        try:
            # Create a copy of the request
            adjusted = request.copy()
            location = adjusted['insertText']['location']
            current_index = location.get('index', 1)

            # Try moving the index to a safer position
            # Google Docs documents typically start at index 1
            if current_index < 1:
                location['index'] = 1
                logger.debug(f"Adjusted index from {current_index} to 1")
                return adjusted

            # For other cases, try the next index
            location['index'] = current_index + 1
            logger.debug(f"Adjusted index from {current_index} to {current_index + 1}")
            return adjusted

        except Exception as e:
            logger.error(f"Error adjusting request: {e}")
            return None
