"""
Markdown Processor
Converts Obsidian markdown to Google Docs API requests
"""

import re
from pathlib import Path
from typing import List, Dict, Tuple
import markdown
from bs4 import BeautifulSoup
from .utils import logger, grapheme_len, clean_whitespace_lines


class MarkdownProcessor:
    """Processes Obsidian markdown and converts to Google Docs requests"""
    
    def __init__(self, file_manager):
        """
        Initialize the markdown processor
        
        Args:
            file_manager: FileManager instance for finding media files
        """
        self.file_manager = file_manager
    
    def convert_markdown_to_docs_requests(self, markdown_content: str, note_path: Path) -> List[Dict]:
        """Convert Obsidian markdown to Google Docs API requests"""
        try:
            # Clean up whitespace-only lines before processing
            cleaned_content = clean_whitespace_lines(markdown_content)

            # Pre-process nested lists to work around markdown library limitations
            processed_content = self.preprocess_nested_lists(cleaned_content)

            # Convert basic markdown to HTML first
            html = markdown.markdown(processed_content, extensions=['tables', 'fenced_code'])
            soup = BeautifulSoup(html, 'html.parser')

            requests = []
            # Start at index 1 - Google Docs documents start with a newline at index 0
            # and content should be inserted starting at index 1
            index = 1

            # Process content sequentially to handle lists in place
            requests, index = self.process_content_sequentially(processed_content, index)

            # Handle Obsidian-specific syntax (links, etc. - images are handled separately)
            requests = self.process_obsidian_links(requests, markdown_content)

            return requests

        except Exception as e:
            logger.error(f"Error converting markdown: {e}")
            return []

    def process_content_sequentially(self, content: str, start_index: int) -> Tuple[List[Dict], int]:
        """Process markdown content sequentially, handling lists in place"""
        requests = []
        index = start_index
        lines = content.split('\n')
        i = 0

        while i < len(lines):
            line = lines[i]

            # Check if this is the start of a list block
            if self.is_list_item(line):
                # Extract and process the entire list block
                list_lines, next_i = self.extract_list_block_from_lines(lines, i)
                list_requests, list_length = self.process_markdown_list_block(list_lines, index)
                requests.extend(list_requests)
                index += list_length
                i = next_i
                continue

            # Process non-list content using HTML conversion for proper formatting
            # Collect consecutive non-list lines
            non_list_lines = []
            start_i = i
            while i < len(lines) and not self.is_list_item(lines[i]):
                non_list_lines.append(lines[i])
                i += 1

            if non_list_lines:
                # Convert this block to HTML and process
                block_content = '\n'.join(non_list_lines)
                if block_content.strip():  # Only process non-empty blocks
                    html = markdown.markdown(block_content, extensions=['tables', 'fenced_code'])
                    soup = BeautifulSoup(html, 'html.parser')

                    block_requests, block_length = self.process_html_elements(soup, index)
                    requests.extend(block_requests)
                    index += block_length

        return requests, index

    def extract_list_block_from_lines(self, lines: List[str], start_index: int) -> Tuple[List[str], int]:
        """Extract a complete list block starting from the given index"""
        list_lines = []
        i = start_index
        base_indent = self.get_list_indent_level(lines[i]) if i < len(lines) else 0

        while i < len(lines):
            line = lines[i]

            if not line.strip():
                # Empty line - might be part of list or end of list
                list_lines.append(line)
                i += 1
                continue

            if self.is_list_item(line):
                # Another list item
                current_indent = self.get_list_indent_level(line)
                if current_indent >= base_indent:
                    # Part of the same list block
                    list_lines.append(line)
                    i += 1
                else:
                    # New list block at different level - stop here
                    break
            else:
                # Check if it's continuation of a list item
                if line.startswith('  '):
                    list_lines.append(line)
                    i += 1
                else:
                    # End of list block
                    break

        return list_lines, i

    def process_html_elements(self, soup: BeautifulSoup, start_index: int) -> Tuple[List[Dict], int]:
        """Process HTML elements and return requests and total length"""
        requests = []
        index = start_index

        # Process each element in the HTML
        for element in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'table', 'pre']):
            if element.name.startswith('h'):
                # Handle headers
                level = int(element.name[1])
                text = element.get_text()

                requests.append({
                    'insertText': {
                        'location': {'index': index},
                        'text': text + '\n'
                    }
                })

                # Apply heading style
                text_length = grapheme_len(text)
                requests.append({
                    'updateParagraphStyle': {
                        'range': {
                            'startIndex': index,
                            'endIndex': index + text_length
                        },
                        'paragraphStyle': {
                            'namedStyleType': f'HEADING_{level}'
                        },
                        'fields': 'namedStyleType'
                    }
                })

                index += text_length + 1

            elif element.name == 'p':
                # Handle paragraphs
                text = self.process_paragraph_formatting(element)
                if text.strip():
                    requests.append({
                        'insertText': {
                            'location': {'index': index},
                            'text': text + '\n'
                        }
                    })
                    index += grapheme_len(text) + 1

            elif element.name == 'table':
                # Handle tables
                table_requests, table_length = self.process_table(element, index)
                requests.extend(table_requests)
                index += table_length

            elif element.name == 'pre':
                # Handle code blocks
                code_text = element.get_text()
                requests.append({
                    'insertText': {
                        'location': {'index': index},
                        'text': code_text + '\n'
                    }
                })

                # Apply monospace formatting
                code_text_length = grapheme_len(code_text)
                requests.append({
                    'updateTextStyle': {
                        'range': {
                            'startIndex': index,
                            'endIndex': index + code_text_length
                        },
                        'textStyle': {
                            'weightedFontFamily': {
                                'fontFamily': 'Courier New'
                            },
                            'backgroundColor': {
                                'color': {
                                    'rgbColor': {
                                        'red': 0.95,
                                        'green': 0.95,
                                        'blue': 0.95
                                    }
                                }
                            }
                        },
                        'fields': 'weightedFontFamily,backgroundColor'
                    }
                })

                index += code_text_length + 1

        return requests, index - start_index

    def process_paragraph_formatting(self, element) -> str:
        """Process paragraph with inline formatting"""
        text = ""
        for content in element.contents:
            if hasattr(content, 'name'):
                if content.name == 'strong' or content.name == 'b':
                    text += content.get_text()
                elif content.name == 'em' or content.name == 'i':
                    text += content.get_text()
                elif content.name == 'code':
                    text += content.get_text()
                elif content.name == 'a':
                    text += content.get_text()
                else:
                    text += content.get_text()
            else:
                text += str(content)
        return text

    def preprocess_nested_lists(self, content: str) -> str:
        """
        Preprocess markdown content to better handle nested lists.
        The standard markdown library doesn't handle nested lists well,
        so we'll add some preprocessing to improve the structure.
        """
        lines = content.split('\n')
        processed_lines = []

        i = 0
        while i < len(lines):
            line = lines[i]

            # Check if this is a list item
            if self.is_list_item(line):
                # Process this list item and any following nested items
                list_block, next_i = self.process_list_block(lines, i)
                processed_lines.extend(list_block)
                i = next_i
            else:
                processed_lines.append(line)
                i += 1

        return '\n'.join(processed_lines)

    def is_list_item(self, line: str) -> bool:
        """Check if a line is a list item"""
        stripped = line.lstrip()
        # Unordered list: starts with -, *, or +
        if stripped.startswith(('- ', '* ', '+ ')):
            return True
        # Ordered list: starts with number followed by . or )
        if re.match(r'^\d+[.)]\s', stripped):
            return True
        return False

    def get_list_indent_level(self, line: str) -> int:
        """Get the indentation level of a list item"""
        return len(line) - len(line.lstrip())

    def process_list_block(self, lines: List[str], start_index: int) -> Tuple[List[str], int]:
        """Process a block of list items, maintaining proper nesting"""
        processed_lines = []
        i = start_index

        while i < len(lines):
            line = lines[i]

            if not line.strip():
                # Empty line - might be end of list or just spacing
                processed_lines.append(line)
                i += 1
                continue

            if self.is_list_item(line):
                # This is a list item, add it as-is
                processed_lines.append(line)
                i += 1
            else:
                # Not a list item - check if it's continuation of previous item
                if i > start_index and line.startswith('  '):
                    # Looks like continuation, keep it
                    processed_lines.append(line)
                    i += 1
                else:
                    # End of list block
                    break

        return processed_lines, i

    def extract_list_blocks_from_markdown(self, content: str) -> Tuple[List[List[str]], str]:
        """Extract list blocks from markdown and return them separately from other content"""
        lines = content.split('\n')
        list_blocks = []
        non_list_lines = []

        i = 0
        while i < len(lines):
            line = lines[i]

            if self.is_list_item(line):
                # Start of a list block
                list_block = []
                base_indent = self.get_list_indent_level(line)

                # Collect all lines that belong to this list block
                while i < len(lines):
                    current_line = lines[i]

                    if not current_line.strip():
                        # Empty line - might be part of list or end of list
                        list_block.append(current_line)
                        i += 1
                        continue

                    if self.is_list_item(current_line):
                        # Another list item
                        current_indent = self.get_list_indent_level(current_line)
                        if current_indent >= base_indent:
                            # Part of the same list block
                            list_block.append(current_line)
                            i += 1
                        else:
                            # New list block at different level
                            break
                    else:
                        # Check if it's continuation of a list item
                        if current_line.startswith('  '):
                            list_block.append(current_line)
                            i += 1
                        else:
                            # End of list block
                            break

                if list_block:
                    list_blocks.append(list_block)
                    # Add placeholder for list position
                    non_list_lines.append(f"__LIST_BLOCK_{len(list_blocks)-1}__")
            else:
                non_list_lines.append(line)
                i += 1

        return list_blocks, '\n'.join(non_list_lines)

    def process_markdown_list_block(self, list_lines: List[str], start_index: int) -> Tuple[List[Dict], int]:
        """Process a list block directly from markdown lines"""
        requests = []
        total_length = 0

        # Parse the list structure
        list_items = self.parse_list_structure(list_lines)

        # Convert each item to Google Docs requests
        for item in list_items:
            text = item['text'] + '\n'
            text_length = grapheme_len(text)

            # Insert text
            requests.append({
                'insertText': {
                    'location': {'index': start_index + total_length},
                    'text': text
                }
            })

            # Apply bullet formatting based on level
            bullet_preset = self.get_bullet_preset_for_level(item['level'])
            requests.append({
                'createParagraphBullets': {
                    'range': {
                        'startIndex': start_index + total_length,
                        'endIndex': start_index + total_length + text_length - 1
                    },
                    'bulletPreset': bullet_preset
                }
            })

            total_length += text_length

        return requests, total_length

    def parse_list_structure(self, list_lines: List[str]) -> List[Dict]:
        """Parse list lines into structured items with levels"""
        items = []
        indent_levels = []  # Track unique indent levels to calculate nesting

        # First pass: collect all unique indent levels
        for line in list_lines:
            if line.strip() and self.is_list_item(line):
                indent_level = self.get_list_indent_level(line)
                if indent_level not in indent_levels:
                    indent_levels.append(indent_level)

        # Sort indent levels to map them to nesting levels
        indent_levels.sort()

        for line in list_lines:
            if not line.strip():
                continue

            if self.is_list_item(line):
                indent_level = self.get_list_indent_level(line)
                # Map indent level to nesting level based on position in sorted list
                nesting_level = indent_levels.index(indent_level) if indent_level in indent_levels else 0

                # Extract text content
                stripped = line.lstrip()
                # Remove list marker
                if stripped.startswith(('- ', '* ', '+ ')):
                    text = stripped[2:].strip()
                else:
                    # Ordered list - remove number and dot/paren
                    match = re.match(r'^\d+[.)]\s*(.*)$', stripped)
                    if match:
                        text = match.group(1).strip()
                    else:
                        text = stripped

                items.append({
                    'text': text,
                    'level': nesting_level,
                    'indent': indent_level
                })

        return items

    def get_bullet_preset_for_level(self, level: int) -> str:
        """Get appropriate bullet preset based on nesting level"""
        # Google Docs supports different bullet styles for different levels
        bullet_presets = [
            'BULLET_DISC_CIRCLE_SQUARE',      # Level 0: •
            'BULLET_DIAMONDX_ARROW3D_SQUARE', # Level 1: ◆
            'BULLET_CHECKBOX',                 # Level 2: ☐
            'BULLET_ARROW_DIAMOND_DISC',      # Level 3: ➤
            'BULLET_STAR_CIRCLE_SQUARE',      # Level 4: ★
            'BULLET_DISC_CIRCLE_SQUARE'       # Level 5+: back to •
        ]

        # Use modulo to cycle through presets for very deep nesting
        return bullet_presets[level % len(bullet_presets)]

    def process_table(self, element, start_index: int) -> Tuple[List[Dict], int]:
        """Process table elements with proper Google Docs table support"""
        requests = []

        # Extract table data
        table_data = self.extract_table_data(element)

        if not table_data or len(table_data) == 0:
            # Empty table, just add a placeholder
            requests.append({
                'insertText': {
                    'location': {'index': start_index},
                    'text': '[Empty Table]\n'
                }
            })
            return requests, grapheme_len('[Empty Table]') + 1

        rows = len(table_data)
        cols = max(len(row) for row in table_data) if table_data else 1

        # For now, use simple text-based table as Google Docs table API is complex
        # and requires careful handling of table structure and cell references
        logger.debug(f"Processing table with {rows} rows and {cols} columns")

        # Use fallback text-based table formatting
        return self.create_simple_table_fallback(table_data, start_index)

    def extract_table_data(self, table_element) -> List[List[str]]:
        """Extract table data from HTML table element"""
        table_data = []

        # Find all rows
        rows = table_element.find_all('tr')

        for row in rows:
            row_data = []
            # Find all cells (th or td)
            cells = row.find_all(['th', 'td'])

            for cell in cells:
                # Get cell text and clean it up
                cell_text = cell.get_text().strip()
                # Handle line breaks within cells
                cell_text = cell_text.replace('\n', ' ').replace('\r', ' ')
                # Collapse multiple spaces
                cell_text = ' '.join(cell_text.split())
                row_data.append(cell_text)

            if row_data:  # Only add non-empty rows
                table_data.append(row_data)

        return table_data

    def create_simple_table_fallback(self, table_data: List[List[str]], start_index: int) -> Tuple[List[Dict], int]:
        """Create a simple text-based table as fallback when Google Docs table API fails"""
        requests = []
        table_text = ""

        if not table_data:
            return requests, 0

        # Calculate column widths for better formatting
        col_widths = []
        max_cols = max(len(row) for row in table_data) if table_data else 0

        for col_idx in range(max_cols):
            max_width = 0
            for row in table_data:
                if col_idx < len(row):
                    max_width = max(max_width, len(row[col_idx]))
            col_widths.append(min(max_width, 20))  # Cap at 20 characters

        # Create table header separator
        separator = "+" + "+".join(["-" * (width + 2) for width in col_widths]) + "+\n"

        # Format each row
        for row_idx, row_data in enumerate(table_data):
            # Add separator before first row and after header
            if row_idx == 0 or row_idx == 1:
                table_text += separator

            # Format row
            formatted_cells = []
            for col_idx in range(max_cols):
                cell_text = row_data[col_idx] if col_idx < len(row_data) else ""
                width = col_widths[col_idx]
                formatted_cells.append(f" {cell_text:<{width}} ")

            table_text += "|" + "|".join(formatted_cells) + "|\n"

        # Add final separator
        table_text += separator + "\n"

        requests.append({
            'insertText': {
                'location': {'index': start_index},
                'text': table_text
            }
        })

        return requests, grapheme_len(table_text)

    def process_obsidian_links(self, requests: List[Dict], markdown_content: str) -> List[Dict]:
        """Process Obsidian-specific syntax like [[links]]"""
        # Handle Obsidian internal links [[link]]
        # For now, we'll just keep them as text since Google Docs doesn't have equivalent
        # In the future, this could be enhanced to create actual links between documents

        return requests

    def process_images_in_markdown(self, markdown_content: str, note_path: Path) -> Tuple[str, List[Tuple[str, Path, str]]]:
        """Process markdown and return modified content with image placeholders"""
        image_positions = []
        # Clean whitespace-only lines first
        modified_content = clean_whitespace_lines(markdown_content)

        # Find embedded images and their positions
        embedded_image_pattern = r'!\[\[([^\]]+)\]\]'

        for match in re.finditer(embedded_image_pattern, markdown_content):
            image_name = match.group(1)
            image_path = self.file_manager.find_media_file(note_path.parent, image_name)

            if image_path and image_path.exists():
                # Create a unique placeholder that we can find later
                placeholder_id = f"IMAGE_PLACEHOLDER_{len(image_positions)}"
                image_positions.append((image_name, image_path, placeholder_id))

                logger.info(f"Found embedded image: {image_name}")

        # Replace all image syntax with placeholders
        for image_name, image_path, placeholder_id in image_positions:
            image_pattern = rf'!\[\[{re.escape(image_name)}\]\]'
            placeholder_text = f"\n[{placeholder_id}]\n"
            modified_content = re.sub(image_pattern, placeholder_text, modified_content)

        return modified_content, image_positions
