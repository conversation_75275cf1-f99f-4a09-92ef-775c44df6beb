#!/bin/bash

# Obsidian to OneNote Sync Runner Script

echo "🔄 Obsidian to OneNote Sync Tool"
echo "================================="

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.7+ first."
    exit 1
fi

# Check if pip3 is installed
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is not installed. Please install pip3 first."
    exit 1
fi

# Install dependencies if requirements.txt exists
if [ -f "requirements.txt" ]; then
    echo "📦 Installing dependencies..."
    pip3 install -r requirements.txt
else
    echo "❌ requirements.txt not found!"
    exit 1
fi

# Create example vault if obsidian folder doesn't exist
if [ ! -d "obsidian" ]; then
    echo "📁 Creating example Obsidian vault..."
    python3 setup_example.py
fi

# Create config if it doesn't exist
if [ ! -f "sync_config.json" ]; then
    echo "⚙️  Creating configuration file..."
    python3 obsidian_to_onenote_sync.py --create-config
    echo ""
    echo "⚠️  Please edit sync_config.json with your Microsoft App credentials before continuing."
    echo "   You need to:"
    echo "   1. Create an Azure App Registration"
    echo "   2. Get the Client ID"
    echo "   3. Update the client_id field in sync_config.json"
    echo ""
    read -p "Press Enter after you've updated the configuration file..."
fi

# Run the sync
echo "🚀 Starting sync process..."
python3 obsidian_to_onenote_sync.py

echo "✅ Sync process completed!"
echo "📋 Check sync.log for detailed information."
